
# Геосервис -  картографический сервис и административная панель проекта **НПИ**

## Cтек:
  - Python 3.10 
  - Django 3.0
  - Leaflet
  - Postgres 11+
  - Postgis

## Запуск проекта(с нуля):
### Создать базу данных, назначить роль с правами суперпользователя.
### Задеплоить приложение
### Подключиться к поду
### Выполнить миграции моделей данных:
  ```python
  - python manage.py makemigrations 
  - python manage.py migrate
  ```
  ### Cоздать суперпользователя django:
  ```python
  - python manage.py createsuperuser
  ```
### Загрузить данные в базу из дампа:
  ```bash
  - psql -U psql_user -d psql_geodatabase -f /database_dump.sql
  ```

## Доступ к сервису
### Админка:
https://url/prefix/admin/
### Карта:
https://url/prefix/layer_map/
### Get-метод
https://url/prefix/get_layer_polygons


>*Для проекта необходимо настроить проксирование на вебсервере:*
>
>  location /prefix/static   
>  location /prefix/admin 
>  location /prefix/layer_map   
>  location /prefix/get_layer_polygons 
