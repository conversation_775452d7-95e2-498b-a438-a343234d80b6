"""
URL configuration for post_admin project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views
from mapping.forms import MyAuthenticationForm
from django.conf import settings

pref=settings.KAZPOSTGEO_PREFIX+'/'
#pref=''

from mapping import views

from mapping.admin import hubs_editor_admin_view

urlpatterns = [
    path(pref+"admin/hubs-editor/", hubs_editor_admin_view, name="hubs_editor_admin"),
    path(pref+"admin/", admin.site.urls),
    path(pref+"layer_map/", views.layer_map, name="layer_map"),
    path(pref+"get_layer_polygons/", views.get_layer_polygons, name="get_layer_polygons"),
    path(pref+"get_departments/", views.get_departments, name="get_departments"),
    path(pref+"save_polygon/", views.save_polygon, name="save_polygon"),
    path(pref+"bulk_save_hubs/", views.bulk_save_hubs, name="bulk_save_hubs"),
    path(pref+"get_points_outside_hubs/", views.get_points_outside_hubs, name="get_points_outside_hubs"),
    path(pref+"update_department/", views.UpdateDepartment.as_view(), name="update_department"),
    path(pref+"update-department/", views.UpdateDepartment.as_view(), name="update-department"),
    #path(pref+"login/", auth_views.LoginView.as_view(template_name='login2.html', authentication_form=MyAuthenticationForm), name='login'),
    path(pref+"login/", views.login_view, name='login'),
    path(pref+"login/", auth_views.LogoutView.as_view(), name='logout'),
    path(pref+'download/<str:filename>/', views.download_file, name='download_file')
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
